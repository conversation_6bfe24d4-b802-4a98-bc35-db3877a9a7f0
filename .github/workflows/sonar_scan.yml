# This workflow will build a Java project with <PERSON><PERSON>, and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-java-with-maven

# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: (Auto) Sonar Scan

on: pull_request

env:
  ARTIFACTORY_DOCKER_REGISTRY: ${{ vars.ARTIFACTORY_DOCKER_REGISTRY }}
  ARTIFACTORY_DOCKER_REPO: ${{ vars.ARTIFACTORY_DOCKER_REPO }}
  ARTIFACTORY_URL: ${{ vars.ARTIFACTORY_URL }}
  HELM_REPO: ${{ vars.HELM_REPO }}
  VAULT_ADDRESS_PROD: ${{ vars.VAULT_ADDRESS_PROD }}
  VAULT_GITHUB_ROLE: ${{ vars.VAULT_GITHUB_ROLE }}
  VAULT_GITHUB_PATH: ${{ vars.VAULT_GITHUB_PATH }}
  vars: ${{ toJSON(vars) }}

jobs:
  sonar_scan:
    permissions:
      contents: read
      statuses: write
      id-token: write
    runs-on:
      - kt-gu-cn
    steps:
    - uses: kering-technologies-china/dso-cus-github-action/microservice/setup@main
      name: Setup Environment
      with:
        java-version: 21
        maven-version: 3.9.6
    - name: Sonar Scan
      uses: kering-technologies-china/dso-cus-github-action/maven-sonar-scan@main
      with:
        maven-test: "true"
