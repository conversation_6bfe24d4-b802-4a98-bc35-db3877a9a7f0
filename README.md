# Microservice cus-demo-app

## Overview

cus-demo-app is a model microservice.If you need create a new microservice in cus,you can use this template to quickly build your own service

## Unit Test

Code coverage: current -> 81%

```
mvn -B clean test jacoco:report --file pom.xml
mvn -B sonar:sonar -Dsonar.host.url=https://sonar.mgmt.arsenal.mgmt4apps.io  -Dsonar.token=squ_* --file pom.xml
```

sonar.coverage.exclusions
```xml
<sonar.coverage.exclusions>
  /src/main/java/**/DemoApplication.java,
  /src/main/java/**/dto/*.java,
  /src/main/java/**/dao/*.java,
  /src/main/java/**/mapper/*.java,
  /src/main/java/**/entity/*.java,
  /src/main/java/**/config/*.java,
  /src/main/java/**/client/**/*.java,
  /src/main/java/**/converter/*.java,
  /src/main/java/**/exception/*.java,
  /src/main/java/**/constant/*.java
</sonar.coverage.exclusions>
```

## How to run the application locally

### Copy application.properties

1. First cope the application.properties.template to application-dev.properties
2. Then Use the following command to build the project

```bash
mvn clean package
```

3. Finally run the application using the following command

```bash
java -jar target/cus-demo-app-1.0.0-SNAPSHOT.jar
```

or

Import the project into IntelliJ IDEA and run the project

### Compile Errors

```compile error
PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException:
unable to find valid certification path to requested target and 'parent.relativePath' points at no local
```

This error indicates that there is an SSL error when connects the [rep.maven.apache.org] to the happens when in the kering intranet,
You can comment out the repository settings or use the public edge node.

Reference: https://stackoverflow.com/questions/25911623/problems-using-maven-and-ssl-behind-proxy

It is recommended to use Maven setting.xml for repository configuration
```xml
<?xml version="1.0" encoding="utf-8"?>

<settings xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0                       http://maven.apache.org/xsd/settings-1.0.0.xsd">
  <localRepository>{your path}</localRepository>
  <servers>
    <server>
      <id>cus-lib-snapshot</id>
      <username><EMAIL></username>
      <password>***</password>
    </server>
    <server>
      <id>cus-lib-release</id>
      <username><EMAIL></username>
      <password>***</password>
    </server>
  </servers>
  <pluginGroups>
    <pluginGroup>org.sonarsource.scanner.maven</pluginGroup>
    <pluginGroup>com.spotify</pluginGroup>
  </pluginGroups>
  <profiles>
    <profile>
      <id>default</id>
      <repositories>
        <repository>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>cus-lib-release</id>
          <name>KT Framework Release Repository</name>
          <url>https://artifactory-cn.kering.cn/artifactory/keringtech-maven-remote-cus-alicloud-edge/</url>
        </repository>
        <repository>
          <releases>
            <enabled>false</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
          <id>cus-lib-snapshot</id>
          <name>KT Framework Snapshot Repository</name>
          <url>https://artifactory-cn.kering.cn/artifactory/keringtech-maven-snapshot-remote-cus-alicloud-edge/</url>
        </repository>
      </repositories>
    </profile>
  </profiles>
  <mirrors>
    <mirror>
      <id>alimaven</id>
      <name>aliyun maven</name>
      <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
      <mirrorOf>central</mirrorOf>
    </mirror>
  </mirrors>
  <activeProfiles>
    <activeProfile>default</activeProfile>
  </activeProfiles>
</settings>
```

### Property file

Principally we should define the basic settings in the library and only leave the customized properties in the property files
for helm injection.

Please see more detail explained in application.properties.template

### Install the postgresql

see: https://hub.docker.com/_/postgres

```
docker pull postgres
docker run -d -p 5432:5432 --name postgres -e POSTGRES_PASSWORD=postgres postgres
create database db_kering_dev
```
Run DML and DDL scripts in sqlscripts directory

## Project Structure

src/main/java (com.kering.cus.demo)
- client - Put all other services' client invoker in this folder
- client/request - Put all request model definitions in this folder for request other services
- client/response - Put all response model definitions in this folder for request other services
- config - Put all spring related configurations in this folder
- constant - Put all constant definitions in this folder
- controller - Put all rest endpoints in the controller
- converter - Put all converter definitions in this folder
- dao - Put all DAO definitions in this folder
- dto - Put all DTO model definitions in this folder
- entity - Put all entity definitions in this folder
- exception - Put all custom exceptions in this folder
- mapper - Put all mapper definitions in this folder
- service - Put all service layers in this folder
  src/main/resources
- i18n - The message property files for error message
- application.properties.template - The application property template file
- application-[env].properties - The application property file specific for each env dev/qa/preprod/prod
- logback.xml - The log layout configuration

## Tech Stack

- Java 21 + Spring Boot 3.3.1 + Spring 6
- Maven 3.9.2
- PostgreSQL
- Spring Actuator
- Spring MVC
- Jackson Object Mapper
- MapStruct
- Swagger 3
- Sonar
- Spotbugs
- JUnit + mockito
- Docker

## Domain in kubernetes nonprod for dev/qa/preprod

- Dev - https://dev-private-ingress.cus-nonprod.keringcn-syss.net
- QA - https://qa-private-ingress.cus-nonprod.keringcn-syss.net
- Preprod - https://preprod-private-ingress.cus-nonprod.keringcn-syss.net

## Localhost

### Spring Boot Actuator Endpoints

- Base Actuator: http://localhost:8080/actuator
- Liveness Probe: http://localhost:8080/actuator/health/liveness
- Readiness Probe: http://localhost:8080/actuator/health/readiness
- Prometheus metric: http://localhost:8080/actuator/prometheus

### Swagger 3

- Swagger UI - http://localhost:8080/swagger-ui/index.html
- Swagger json file - http://localhost:8080/v3/api-docs

### cus-app-sample-service API

#### 1、save demoinfo
```bash
curl --location 'http://localhost:8080/demo' \
--header 'X-CUS-Tenant-Id: kering' \
--header 'X-CUS-WeCom-Email: <EMAIL>' \
--header 'Content-Type: application/json' \
--data '{
    "userId":"JMSH4230",
    "name":"tony",
    "position":"position"
}'
```

#### 2、delete demoinfo by userId
```bash
curl --location --request DELETE 'http://localhost:8080/demo/JMSH4230' \
--header 'X-CUS-Tenant-Id: kering'
```

#### 3、update demoinfo by id
```bash
curl --location --request PUT 'http://localhost:8080/demo' \
--header 'X-CUS-Tenant-Id: kering' \
--header 'Content-Type: application/json' \
--data '{   
    "id":"a8f9b8340bb42fb15c878d23397da923",
    "name":"ted"
}'
```

#### 4、get demo info by userId
```bash
curl --location 'http://localhost:8080/demo/JMSH4230/info' \
--header 'X-CUS-Tenant-Id: kering'
```

#### 5、get demo info list by page
```bash
curl --location 'http://localhost:8080/demo/list?position=position&pageNumber=1&pageSize=10&sort=created_date%2CDESC' \
--header 'X-CUS-Tenant-Id: kering'
```

#### 5、get accessToken of wechat miniProgram from wechat server
```bash
curl --location 'http://localhost:8080/demo/getAccessToken'
```

## Integrations

For all the depended middlewares or facilities, we must follow the convention to decouple the feature and config, in kering
technology, we put all configured properties in the helm values for all environments

### How to integrate the postgres database

Local developer machine
```
# Database Settings
database.url=*****************************************
database.username=postgres
database.password=postgres
```

- database.url - the database connection url
- database.username - the database credential's username
- database.password - the database credential's password

Noted: the credentials need to be secured and should never be configured in the application-[env].property, in this case
the developer can configure the db creds in the local application.property file to start the application, but for the real
environment, we will configure the username and password in vault, and injected via the Kubernetes secrets which is to be mapped
to the pod file, the datasource build logic is implemented in the cus-lib-persistence-support.

```Helm ENV
DATABASE_URL: get the database url from devOps personnel
```
The DATABASE_URL will be injected to the pod environment, and then the container will start with the injected url and pass
to the java application via *-Dpersistence.database.url=${DATABASE_URL}*

```Helm Secrets
DATABASE_USERNAME: <DATABASE_USERNAME>
DATABASE_PASSWORD: <DATABASE_PASSWORD>
```

### How to integrate with cus-lib-log-support

The trace implementation id the responsibility of cus-lib-log-support, add @Probe annotation to any method of the controller,
service can easily print the log in json format, and report the trace/span to the AliCloud OpenTelemetry

```java
@GetMapping(value = "/{userId}/info")
@Operation(summary = "getDemoInfo")
@ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "getDemoInfo",
                content = { @Content(schema = @Schema(implementation = DemoInfoDTO.class)) })})
@Probe(event = "getDemoInfo", group = "controller")
public ResponseEntity<DemoInfoDTO> getDemoInfo(@PathVariable("userId") @ProbeDimension(name = "userId") String userId){
        DemoInfoDTO demo = demoService.findDemoByUserId(userId);
        return ResponseEntity.status(HttpStatus.OK).body(demo);
        }
```
you can get detail information in the [cus-lib-log-support](https://github-cn.kering.cloud/kering-technologies-china/cus-lib/tree/main/cus-lib-log-support)

### How to integrate with cus-lib-persistence-mybatis-support

1、add the cus-lib-persistence-mybatis-support dependency in pom.xml
```
<dependency>
<groupId>com.kering.cus.lib</groupId>
<artifactId>cus-lib-persistence-mybatis-support</artifactId>
<version>${cus-lib.version}</version>
</dependency>
```

2、define your entity
```java
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "demo")
public class DemoEntity extends SoftDeleteMyBatisBaseEntity<String> {

    /** the userId of employee*/
    @TableField(value = "user_id")
    private String userId;

    /** the name of employee*/
    @TableField(value = "name")
    private String name;
    
    ....
}
```

3、define your mapper interface
```java
@Mapper
public interface DemoMapper extends BaseMapper<DemoEntity> {

}
```
4、define your dao
```java
@Repository
public class DemoDAO extends MyBatisBaseDAO<DemoEntity, DemoMapper, String> {

  /**
   * find demo by user id
   * @param userId the id of user
   * @return the user info in db
   */
  public DemoEntity findDemoByUserId(String userId) {
    LambdaQueryWrapper<DemoEntity> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(DemoEntity::getUserId, userId);
    return entityMapper.selectOne(wrapper);
  }

}
```

you can get detail information in the [cus-lib-persistence-mybatis-support](https://github-cn.kering.cloud/kering-technologies-china/cus-lib/tree/main/cus-lib-persistence-mybatis-support)

### How to integrate with Open Telemetry

We have integrated it in the Dockerfile. If you want to report trace data when starting locally, you can add the following command when starting your jar package.

```
    -javaagent:opentelemetry-javaagent-${APM_AGENT_VERSION}.jar \
    -Dotel.exporter.otlp.protocol=http/protobuf \
    -Dotel.resource.attributes=service.name=auth-facade,deployment.environment=dev \
    -Dotel.exporter.otlp.traces.endpoint=${ALICLOUD_OTEL_URL}/adapt_${ALICLOUD_OTEL_TOKEN}/api/otlp/traces \
    -Dotel.logs.exporter=none \
    -Dotel.metrics.exporter=none \
    -Dotel.trace.exporter=otlp \
    -Dotel.javaagent.logging=application \
    -jar app.jar
```

```Helm Env
ALICLOUD_OTEL_URL:  http://tracing-analysis-dc-hz-internal.aliyuncs.com
```

```Helm Secret
ALICLOUD_OTEL_TOKEN: <OTEL_TOKEN>
```
