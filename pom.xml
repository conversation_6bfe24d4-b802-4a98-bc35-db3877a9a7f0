<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.1</version>
		<relativePath/>
	</parent>

	<groupId>com.kering.cus</groupId>
	<artifactId>cus-demo-app</artifactId>
	<name>cus-demo-app</name>
	<version>1.0.0-SNAPSHOT</version>
	<description>the sample service for quickly build a microservice application</description>

	<properties>
		<maven.compiler.source>8</maven.compiler.source>
		<maven.compiler.target>8</maven.compiler.target>
		<maven-compiler-plugin>3.12.1</maven-compiler-plugin>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<cus-lib.version>1.32.0</cus-lib.version>
		<sonar-plugin.version>3.10.0.2594</sonar-plugin.version>
		<jacoco-plugin.version>0.8.12</jacoco-plugin.version>
		<spotbugs-plugin.version>*******</spotbugs-plugin.version>
		<findsecbugs-plugin.version>1.12.0</findsecbugs-plugin.version>
		<properties-maven-plugin.version>1.0.0</properties-maven-plugin.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib</artifactId>
				<version>${cus-lib.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib-rest-provider-support</artifactId>
				<version>${cus-lib.version}</version>
			</dependency>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib-rest-consumer-support</artifactId>
				<version>${cus-lib.version}</version>
			</dependency>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib-persistence-mybatis-support</artifactId>
				<version>${cus-lib.version}</version>
			</dependency>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib-log-support</artifactId>
				<version>${cus-lib.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-rest-provider-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-rest-consumer-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-persistence-mybatis-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-log-support</artifactId>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-processor</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.sonarsource.scanner.maven</groupId>
				<artifactId>sonar-maven-plugin</artifactId>
				<version>${sonar-plugin.version}</version>
				<executions>
					<execution>
						<phase>verify</phase>
						<goals>
							<goal>sonar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>${jacoco-plugin.version}</version>
				<executions>
					<execution>
						<id>prepare-jacoco</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
<!--			<plugin>-->
<!--				<groupId>com.github.spotbugs</groupId>-->
<!--				<artifactId>spotbugs-maven-plugin</artifactId>-->
<!--				<version>${spotbugs-plugin.version}</version>-->
<!--				<configuration>-->
<!--					<excludeFilterFile>spotbugs-security-exclude.xml</excludeFilterFile>-->
<!--					<plugins>-->
<!--						<plugin>-->
<!--							<groupId>com.h3xstream.findsecbugs</groupId>-->
<!--							<artifactId>findsecbugs-plugin</artifactId>-->
<!--							<version>${findsecbugs-plugin.version}</version>-->
<!--						</plugin>-->
<!--					</plugins>-->
<!--				</configuration>-->
<!--				<executions>-->
<!--					<execution>-->
<!--						<id>check</id>-->
<!--						<phase>test</phase>-->
<!--						<goals>-->
<!--							<goal>check</goal>-->
<!--						</goals>-->
<!--					</execution>-->
<!--				</executions>-->
<!--			</plugin>-->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>properties-maven-plugin</artifactId>
				<version>${properties-maven-plugin.version}</version>
				<executions>
					<execution>
						<phase>initialize</phase>
						<goals>
							<goal>read-project-properties</goal>
						</goals>
						<configuration>
							<files>
								<file>sonar-project.properties</file>
							</files>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
