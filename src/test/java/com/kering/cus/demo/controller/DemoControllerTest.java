package com.kering.cus.demo.controller;

import com.kering.cus.demo.dto.DemoInfoDTO;
import com.kering.cus.demo.service.DemoService;
import com.kering.cus.lib.common.PlatformHeaders;
import com.kering.cus.lib.common.context.GenericRequestContextHolder;
import com.kering.cus.lib.common.context.bo.IdentityContext;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DemoControllerTest {

    @InjectMocks
    private DemoController demoController;

    @Mock
    private DemoService mockDemoService;

    @BeforeAll
    public static void beforeAll() {
        Mockito.clearAllCaches();
        mockStatic(GenericRequestContextHolder.class);
    }

    @Test
    void givenUserId_whenCallGetDemoInfo_thenReturnDemoInfoDTO() {
        //expected
        DemoInfoDTO demoInfoDTO = DemoInfoDTO.builder()
                .userId("userId")
                .name("name")
                .departmentIds("departmentIds")
                .position("position")
                .email("email")
                .wkId("wkId")
                .build();

        when(mockDemoService.findDemoByUserId("userId")).thenReturn(demoInfoDTO);

        ResponseEntity<DemoInfoDTO> response = demoController.getDemoInfo("userId");

        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    void givenDemoInfoDTO_whenCallCreateDemoInfo_thenReturnVoid() {
        DemoInfoDTO demoInfoDTO = DemoInfoDTO.builder()
                .userId("userId")
                .name("name")
                .departmentIds("departmentIds")
                .position("position")
                .email("email")
                .wkId("wkId")
                .build();

        IdentityContext identityContext = new IdentityContext();
        Map<String, String> identityMap  = new HashMap<>();
        identityMap.put(PlatformHeaders.WECOM_EMAIL.name(), "<EMAIL>");
        identityContext.setRequestAttributeMaps(identityMap);
        Mockito.when(GenericRequestContextHolder.getIdentityContext()).thenReturn(Optional.of(identityContext));

        ResponseEntity<Void> response = demoController.createDemoInfo(demoInfoDTO);
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
    }
}
