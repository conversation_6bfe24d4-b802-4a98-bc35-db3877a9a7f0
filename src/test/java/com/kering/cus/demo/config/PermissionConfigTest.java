package com.kering.cus.demo.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class PermissionConfigTest {

    @Autowired
    private PermissionConfig permissionConfig;

    @Test
    void testSenderPropertiesLoaded() {
        assertNotNull(permissionConfig.getSenderProperties());
        assertFalse(permissionConfig.getSenderProperties().isEmpty());
        
        System.out.println("Loaded keys: " + permissionConfig.getSenderProperties().keySet());
        
        permissionConfig.getSenderProperties().forEach((key, value) -> {
            System.out.println("Key: '" + key + "' -> " + value);
        });
    }
}
