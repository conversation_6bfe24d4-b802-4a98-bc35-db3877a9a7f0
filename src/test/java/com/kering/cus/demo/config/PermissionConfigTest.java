package com.kering.cus.demo.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class PermissionConfigTest {

    @Autowired
    private PermissionConfig permissionConfig;

    @Test
    void testSenderPropertiesLoaded() {
        assertNotNull(permissionConfig.getSenderProperties());
        assertFalse(permissionConfig.getSenderProperties().isEmpty());

        System.out.println("Loaded keys: " + permissionConfig.getSenderProperties().keySet());

        permissionConfig.getSenderProperties().forEach((key, value) -> {
            System.out.println("Key: '" + key + "' -> " + value);
        });
    }

    @Test
    void testGetConfigByBusinessId() {
        // 测试获取业务ID为1的配置
        PermissionConfig.Config config1 = permissionConfig.getConfigByBusinessId(1);
        assertNotNull(config1);
        assertEquals(1, config1.businessId());
        assertEquals("角色/应用权限申请单", config1.subject());

        // 测试获取业务ID为-2的配置
        PermissionConfig.Config config2 = permissionConfig.getConfigByBusinessId(-2);
        assertNotNull(config2);
        assertEquals(-2, config2.businessId());
        assertEquals("juese2", config2.subject());

        // 测试不存在的业务ID
        PermissionConfig.Config configNotFound = permissionConfig.getConfigByBusinessId(999);
        assertNull(configNotFound);
    }
}
