database:
  password: 123456
  url: ********************************/
  username: root
demo:
  client-id: 123456
test:
  email-sender:
    "1":
      systemId: 123
    "-2":
      systemId: 456

localhost:
  okta:
    client-id: 0oans8cl2dK98ISFn417
    client-secret:

auth-permission:
  rest:
    endpoint: https://dev-galassia-cn-api.kering.cn/cus-api/auth-permission/v1

permission-request:
  email-sender:
    sender-properties:
      "1":
        system-id: 1942117441634484226
        sender-id: 8
        template-id: 27
        subject: "角色/应用权限申请单"
      \"-1\":
        system-id: 1942130634004021248
        sender-id: 15
        template-id: 34
        subject: "juese"
      "2":
        system-id: 1942130634004021222
        sender-id: 15
        template-id: 34
        subject: "juese"
      "-2":
        system-id: 1942130634004021223
        sender-id: 15
        template-id: 34
        subject: "juese"

# 服务器配置
server:
  tomcat:
    mbeanregistry:
      enabled: true
  port: 8083
