package com.kering.cus.demo.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemoInfoDTO {

    /** the id of employee*/
    private String id;

    /** the userId of employee*/
    @NotBlank(message = "user_id_empty")
    private String userId;

    /** the name of employee*/
    private String name;

    /** the departmentIds of employee*/
    private String departmentIds;

    /** the position of employee*/
    private String position;

    /** the email of employee*/
    private String email;

    /** the work email of employee*/
    @JsonProperty("wk_id")
    private String wkId;
}
