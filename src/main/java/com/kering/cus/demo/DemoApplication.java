package com.kering.cus.demo;

import com.kering.cus.lib.rest.consumer.exchange.autoinject.HttpExchangesScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(scanBasePackages = "com.kering.cus")
@HttpExchangesScan(basePackages = {"com.kering.cus.demo.client"})
@MapperScan("com.kering.cus.demo.mapper")
public class DemoApplication {

	public static void main(String[] args) {
		SpringApplication.run(DemoApplication.class, args);
	}
}
