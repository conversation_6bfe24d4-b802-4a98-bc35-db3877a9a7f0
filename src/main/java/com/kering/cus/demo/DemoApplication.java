package com.kering.cus.demo;

import com.kering.cus.demo.config.PermissionConfig;
import com.kering.cus.lib.rest.consumer.exchange.autoinject.HttpExchangesScan;
import jakarta.annotation.PostConstruct;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

@SpringBootApplication(scanBasePackages = "com.kering.cus")
@HttpExchangesScan(basePackages = {"com.kering.cus.demo.client"})
@MapperScan("com.kering.cus.demo.mapper")
@EnableConfigurationProperties
public class DemoApplication {

	public static void main(String[] args) {
		SpringApplication.run(DemoApplication.class, args);
	}


    @Autowired
	private PermissionConfig permissionConfig;

	@PostConstruct
	public void test(){
		System.out.println(permissionConfig.getSenderProperties());
	}
}
