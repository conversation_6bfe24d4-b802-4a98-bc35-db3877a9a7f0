package com.kering.cus.demo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kering.cus.lib.persistence.mybatis.implementation.SoftDeleteMyBatisBaseEntity;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "demo")
public class DemoEntity extends SoftDeleteMyBatisBaseEntity<String> {

    /** the userId of employee*/
    @TableField(value = "user_id")
    private String userId;

    /** the name of employee*/
    @TableField(value = "name")
    private String name;

    /** the departmentIds of employee*/
    @TableField(value = "department_ids")
    private String departmentIds;

    /** the position of employee*/
    @TableField(value = "position")
    private String position;

    /** the email of employee*/
    @TableField(value = "email")
    private String email;

    /** the work email of employee*/
    @TableField(value = "wk_id")
    private String wkId;
}
