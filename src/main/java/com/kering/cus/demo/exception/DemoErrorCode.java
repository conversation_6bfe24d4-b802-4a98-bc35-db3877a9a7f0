package com.kering.cus.demo.exception;

import lombok.Getter;

@Getter
public enum DemoErrorCode {

    RESET_USER_SESSION_KEY_ERROR("reset_user_session_key_error", "reset user session key error"),
    USER_ID_EMPTY("user_id_empty", "user id is empty");

    /**
     * error code
     */
    private final String code;
    /**
     * error message
     */
    private final String message;

    static DemoErrorCode getByCode(String code) {
        for (DemoErrorCode errorCode : DemoErrorCode.values()) {
            if (errorCode.getCode().equals(code)) {
                return errorCode;
            }
        }
        return null;
    }

    DemoErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }
}
