package com.kering.cus.demo.exception;

import com.kering.cus.lib.common.exception.BusinessException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
public class DemoBusinessException extends BusinessException {

    public DemoBusinessException(DemoErrorCode demoErrorCode){
        super(demoErrorCode.getCode(), demoErrorCode.getMessage());
    }
}
