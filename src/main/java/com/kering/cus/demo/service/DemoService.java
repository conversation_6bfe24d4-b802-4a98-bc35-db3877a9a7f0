package com.kering.cus.demo.service;

import com.kering.cus.lib.common.SortCriteria;
import com.kering.cus.lib.log.annotation.Probe;
import com.kering.cus.lib.log.annotation.ProbeDimension;
import com.kering.cus.lib.persistence.common.entity.PageResult;
import com.kering.cus.demo.client.WebDemoClient;
import com.kering.cus.demo.client.response.ResetUserSessionKeyResponse;
import com.kering.cus.demo.config.DemoConfig;
import com.kering.cus.demo.constant.DemoConstants;
import com.kering.cus.demo.converter.DemoConverter;
import com.kering.cus.demo.dao.DemoDAO;
import com.kering.cus.demo.dto.DemoInfoDTO;
import com.kering.cus.demo.entity.DemoEntity;
import com.kering.cus.demo.exception.DemoBusinessException;
import com.kering.cus.demo.exception.DemoErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Service
@Slf4j
public class DemoService {

    @Autowired
    private DemoDAO demoDAO;

    @Autowired
    private WebDemoClient webDemoClient;

    @Autowired
    private DemoConverter demoConverter;

    @Autowired
    private DemoConfig demoConfig;


    /**
     * reset user session key
     * @return the user session key
     */
    @Probe(event = "resetUserSessionKey", group = "service")
    public String resetUserSessionKey() {
        log.info("clientId is {}", demoConfig.getClientId());
        ResetUserSessionKeyResponse response = webDemoClient.resetUserSessionkey("accessToken", "openId", "signature", DemoConstants.WECHAT_RESET_USER_SESSION_KEY_SIG_METHOD);
        if (StringUtils.isEmpty(response.getSessionKey())) {
            throw new DemoBusinessException(DemoErrorCode.RESET_USER_SESSION_KEY_ERROR);
        }
        return response.getSessionKey();
    }

    /**
     * find demo by user id
     * @param userId the id of user
     * @return the demo info in db
     */
    @Probe(event = "findDemoByUserId", group = "service")
    public DemoInfoDTO findDemoByUserId(@ProbeDimension(name = "userId") String userId) {
        DemoEntity demoEntity = demoDAO.findDemoByUserId(userId);
        if (ObjectUtils.isEmpty(demoEntity)) {
            return null;
        }
        return DemoInfoDTO.builder()
                .userId(demoEntity.getUserId())
                .name(demoEntity.getName())
                .departmentIds(demoEntity.getDepartmentIds())
                .position(demoEntity.getPosition())
                .email(demoEntity.getEmail())
                .wkId(demoEntity.getWkId())
                .build();
    }

    /**
     * find demo list page by position and sort
     * @param position the position
     * @param pageNo the page number
     * @param pageSize the page size
     * @param sort the sort
     * @return the page result
     */
    @Probe(event = "findDemoListPageByPositionAndSort", group = "service")
    public PageResult<DemoInfoDTO> findDemoListPageByPositionAndSort(@ProbeDimension(name = "position") String position,
                                                                  @ProbeDimension(name = "pageNo") Integer pageNo, @ProbeDimension(name = "pageSize") Integer pageSize, @ProbeDimension(name = "sort") SortCriteria sort) {
        PageResult<DemoEntity> pageResult = demoDAO.findDemoListPageByPositionAndSort(position, pageNo, pageSize, sort);
        return PageResult.<DemoInfoDTO>builder()
                .pageNumber(pageNo)
                .pageSize(pageSize)
                .totalCount(pageResult.getTotalCount())
                .totalPages(pageResult.getTotalPages())
                .data(demoConverter.toDTO(pageResult.getData()))
                .build();
    }

    /**
     * create demo entity
     * @param demoInfoDTO the demo info
     * @return the demo entity
     */
    @Probe(event = "createDemo", group = "service")
    public DemoEntity createDemo(@ProbeDimension(name = "demoInfoDTO") DemoInfoDTO demoInfoDTO) {
        DemoEntity entity = DemoEntity.builder()
                .userId(demoInfoDTO.getUserId())
                .name(demoInfoDTO.getName())
                .departmentIds(demoInfoDTO.getDepartmentIds())
                .position(demoInfoDTO.getPosition())
                .email(demoInfoDTO.getEmail())
                .wkId(demoInfoDTO.getWkId())
                .build();
        return demoDAO.create(entity);
    }

    /**
     * update demo entity
     * @param demoInfoDTO the demo info
     * @return the demo entity
     */
    @Probe(event = "updateDemoEntity", group = "service")
    public DemoEntity updateDemoEntity(@ProbeDimension(name = "demoInfoDTO") DemoInfoDTO demoInfoDTO) {
        DemoEntity entity = DemoEntity.builder()
                .userId(demoInfoDTO.getUserId())
                .name(demoInfoDTO.getName())
                .departmentIds(demoInfoDTO.getDepartmentIds())
                .position(demoInfoDTO.getPosition())
                .email(demoInfoDTO.getEmail())
                .wkId(demoInfoDTO.getWkId())
                .build();
        entity.setId(demoInfoDTO.getId());
        return demoDAO.update(entity);
    }

    /**
     * delete demo entity by user id
     * @param userId the id of user
     * @return the result
     */
    @Probe(event = "deleteByUserId", group = "service")
    public boolean deleteByUserId(@ProbeDimension(name = "userId") String userId) {
        return demoDAO.deleteByUserId(userId) > 0;
    }
}
