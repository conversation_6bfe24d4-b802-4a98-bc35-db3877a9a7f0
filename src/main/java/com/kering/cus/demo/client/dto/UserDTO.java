package com.kering.cus.demo.client.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDTO {
    /**
     * user id
     */
    private String id;
    /**
     * user login name
     */
    @NotBlank(message = "{user_name_empty}")
    private String loginName;
    /**
     * user name
     */
    @NotBlank(message = "{user_login_name_empty}")
    private String userName;
    /**
     * user email
     */
    @Email(message = "{user_email_not_valid}")
    private String email;
    /**
     * user nickname
     */
    private String nickName;
    /**
     * user mobile phone
     */
    private String mobilePhone;
    /**
     * user gender
     */
    private Integer gender;
    /**
     * user disabled status
     */
    @Builder.Default
    private Boolean disabled = false;

    /**
     * user create date
     */
    private ZonedDateTime createdDate;

    /**
     * description of user
     */
    private String description;
}
