package com.kering.cus.demo.client.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResetUserSessionKeyResponse {

    /**
     * the user id
     */
    @JsonProperty("openid")
    private Long openId;

    /**
     * wechat session key
     */
    @JsonProperty("session_key")
    private String sessionKey;

    /**
     * error code
     */
    @JsonProperty("errcode")
    private String errCode;
    /**
     * error message
     */
    @JsonProperty("errmsg")
    private String errMsg;
}
