package com.kering.cus.demo.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.kering.cus.demo.client.dto.UserDTO;
import com.kering.cus.lib.log.annotation.ProbeDimension;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;


/**
 * PmsPermissionClient
 */
@HttpExchange(url = "${auth-permission.rest.endpoint}")
public interface AuthPermissionClient {

    @GetExchange("/roles/{id}/users")
    ResponseEntity<PageDTO<UserDTO>> getRoleUsers(@PathVariable @ProbeDimension String id,
                                                         @RequestParam(value = "pageNumber", defaultValue = "1") @ProbeDimension int pageNumber,
                                                         @RequestParam(value = "pageSize", defaultValue = "20") @ProbeDimension int pageSize);
}
