package com.kering.cus.demo.client;

import com.kering.cus.demo.client.response.ResetUserSessionKeyResponse;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;

@HttpExchange(url = "https://api.weixin.qq.com")
public interface WebDemoClient {

    /**
     * reset user sessionKey
     *
     * @param accessToken
     * @param openId
     * @param signature
     * @param sigMethod
     * @return
     */
    @GetExchange("/wxa/resetusersessionkey")
    ResetUserSessionKeyResponse resetUserSessionkey(@RequestParam(value = "access_token") String accessToken,
                                                    @RequestParam(value = "openid") String openId,
                                                    @RequestParam(value = "signature") String signature,
                                                    @RequestParam(value = "sig_method") String sigMethod);
}
