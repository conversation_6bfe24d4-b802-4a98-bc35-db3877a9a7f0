package com.kering.cus.demo.config;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;

/**
 * Configuration for store permissions
 */
@Data
@Component
@ConfigurationProperties(prefix = "permission-request.email-sender")
public class PermissionConfig {

    private final Map<String, Config> senderProperties = new HashMap<>();

    @PostConstruct
    public void init() {
        System.out.println("Loaded sender properties keys: " + senderProperties.keySet());
        senderProperties.forEach((key, value) -> {
            System.out.println("Key: '" + key + "' (type: " + key.getClass().getSimpleName() + "), Value: " + value);
        });
    }


    public record Config(String systemId, Integer senderId, Integer templateId, String subject) {
    }

}
