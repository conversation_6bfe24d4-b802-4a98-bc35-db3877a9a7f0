package com.kering.cus.demo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Configuration for store permissions
 */
@Data
@Component
@ConfigurationProperties(prefix = "permission-request.email-sender")
public class PermissionConfig {


    private final Map<String, Config> senderProperties = new HashMap<>();

    public record Config(String systemId, Integer senderId, Integer templateId, String subject) {
    }
}
