package com.kering.cus.demo.config;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;

/**
 * Configuration for store permissions
 */
@Data
@Component
@ConfigurationProperties(prefix = "permission-request.email-sender")
public class PermissionConfig {

    private final Map<String, Config> senderProperties = new HashMap<>();

    @PostConstruct
    public void init() {
        System.out.println("Loaded sender properties keys: " + senderProperties.keySet());
        senderProperties.forEach((key, value) -> {
            System.out.println("Key: '" + key + "' (type: " + key.getClass().getSimpleName() + "), Value: " + value);
        });
    }

    /**
     * 根据业务ID获取配置，处理特殊的负数情况
     */
    public Config getConfigByBusinessId(int businessId) {
        String key;
        if (businessId == 1) {
            key = "id_1";
        } else if (businessId == -2) {
            key = "id_minus_2";
        } else {
            key = String.valueOf(businessId);
        }
        return senderProperties.get(key);
    }

    public record Config(String systemId, Integer senderId, Integer templateId, String subject) {
    }

}
