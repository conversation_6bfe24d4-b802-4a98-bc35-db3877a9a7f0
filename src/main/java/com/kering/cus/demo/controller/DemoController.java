package com.kering.cus.demo.controller;

import com.kering.cus.lib.common.SortCriteria;
import com.kering.cus.lib.common.context.GenericRequestContextHolder;
import com.kering.cus.lib.common.context.bo.IdentityContext;
import com.kering.cus.lib.log.annotation.Probe;
import com.kering.cus.lib.log.annotation.ProbeDimension;
import com.kering.cus.lib.persistence.common.entity.PageResult;
import com.kering.cus.demo.dto.DemoInfoDTO;
import com.kering.cus.demo.service.DemoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/demo")
@RestController
@Slf4j
public class DemoController {

    @Autowired
    private DemoService demoService;

    /**
     * create demo info
     * @param demoInfoDTO the demo info
     * @return the created demo info
     */
    @PostMapping
    @Operation(summary = "createDemoInfo")
    @Probe(event = "createDemoInfo", group = "controller")
    public ResponseEntity<Void> createDemoInfo(@RequestBody @ProbeDimension(name = "demoInfoDTO") @Valid DemoInfoDTO demoInfoDTO) {
        String weComEmail = GenericRequestContextHolder.getIdentityContext().map(IdentityContext::getWeComEmail).get();
        log.info("create demo entity, weComEmail: {}", weComEmail);
        demoService.createDemo(demoInfoDTO);
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }

    /**
     * delete demo info
     * @param userId the id of employee
     * @return the result of delete
     */
    @DeleteMapping(value = "/{userId}")
    @Operation(summary = "deleteDemoInfo")
    @Probe(event = "deleteDemoInfo", group = "controller")
    public ResponseEntity<Void> deleteDemoInfo(@PathVariable("userId") @ProbeDimension(name = "userId") String userId){
        demoService.deleteByUserId(userId);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * update demo info
     * @param demoInfoDTO the demo info
     * @return the updated demo info
     */
    @PutMapping
    @Operation(summary = "updateDemoInfo")
    @Probe(event = "updateDemoInfo", group = "controller")
    public ResponseEntity<Void> updateDemoInfo(@RequestBody @ProbeDimension(name = "demoInfoDTO") DemoInfoDTO demoInfoDTO) {
        demoService.updateDemoEntity(demoInfoDTO);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /**
     * get demo info by userId
     * @param userId the id of employee
     * @return the user info, contains userId, email, name
     */
    @GetMapping(value = "/{userId}/info")
    @Operation(summary = "getDemoInfo")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "getDemoInfo",
                    content = { @Content(schema = @Schema(implementation = DemoInfoDTO.class)) })})
    @Probe(event = "getDemoInfo", group = "controller")
    public ResponseEntity<DemoInfoDTO> getDemoInfo(@PathVariable("userId") @ProbeDimension(name = "userId") String userId){
        DemoInfoDTO demo = demoService.findDemoByUserId(userId);
        return ResponseEntity.status(HttpStatus.OK).body(demo);
    }

    /**
     * get demo info list by position
     * @param pageNumber the page number
     * @param pageSize the page size
     * @param position the position of the demo info
     * @param sort the sort of the demo info
     * @return the demo info list
     */
    @GetMapping(value = "/list")
    @Operation(summary = "getDemoList")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "getDemoList",
                    content = { @Content(schema = @Schema(implementation = PageResult.class)) })})
    @Probe(event = "getDemoList", group = "controller")
    public ResponseEntity<PageResult<DemoInfoDTO>> getDemoList(@RequestParam(value = "pageNumber", defaultValue = "1") @ProbeDimension(name = "pageNumber") int pageNumber,
                                                               @RequestParam(value = "pageSize", defaultValue = "20") @ProbeDimension(name = "pageSize") int pageSize,
                                                               @RequestParam(value = "position", required = false) @ProbeDimension(name = "position") String position,
                                                               @RequestParam(value="sort", required = false) @ProbeDimension(name = "sort") SortCriteria sort) {
        return ResponseEntity.status(HttpStatus.OK).body(demoService.findDemoListPageByPositionAndSort(position, pageNumber, pageSize, sort));
    }

    /**
     * get access token from wechat
     * @return the access token
     */
    @GetMapping(value = "/resetUserSessionKey")
    @Operation(summary = "resetUserSessionKey")
    @Probe(event = "resetUserSessionKey", group = "controller")
    public ResponseEntity<String> resetUserSessionKey() {
        return ResponseEntity.status(HttpStatus.OK).body(demoService.resetUserSessionKey());
    }
}
