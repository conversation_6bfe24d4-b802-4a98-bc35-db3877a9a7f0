package com.kering.cus.demo.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.kering.cus.demo.client.AuthPermissionClient;
import com.kering.cus.demo.client.dto.UserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/demo")
@RestController
@Slf4j
public class UserRoleController {

    @Autowired
    private AuthPermissionClient authPermissionClient;

    @GetMapping(value = "/user/{userId}/role")
    public ResponseEntity<PageDTO<UserDTO>> resetUserSessionKey(@PathVariable String userId) {
       return authPermissionClient.getRoleUsers(userId,1,20);
    }
}
