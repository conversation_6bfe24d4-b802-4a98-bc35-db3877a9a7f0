package com.kering.cus.demo.converter;

import com.kering.cus.demo.dto.DemoInfoDTO;
import com.kering.cus.demo.entity.DemoEntity;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DemoConverter {

    /**
     * DemoEntity convert to DemoInfoDTO
     * @param demoEntity the entity to be converted
     * @return the converted dto
     */
    DemoInfoDTO toDTO(DemoEntity demoEntity);

    List<DemoInfoDTO> toDTO(List<DemoEntity> demoEntityList);
}
