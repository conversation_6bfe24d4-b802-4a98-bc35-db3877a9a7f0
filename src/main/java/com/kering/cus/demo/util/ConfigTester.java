package com.kering.cus.demo.util;

import com.kering.cus.demo.config.PermissionConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class ConfigTester implements CommandLineRunner {

    @Autowired
    private PermissionConfig permissionConfig;

    @Override
    public void run(String... args) throws Exception {
        System.out.println("=== 配置测试开始 ===");
        System.out.println("所有配置键: " + permissionConfig.getSenderProperties().keySet());
        
        permissionConfig.getSenderProperties().forEach((key, value) -> {
            System.out.println("Key: '" + key + "' -> " + value);
        });
        
        // 测试特定的键
        System.out.println("\n=== 特定键测试 ===");
        testKey("1");
        testKey("-1");
        testKey("-2");
        
        System.out.println("=== 配置测试结束 ===");
    }
    
    private void testKey(String key) {
        PermissionConfig.Config config = permissionConfig.getSenderProperties().get(key);
        if (config != null) {
            System.out.println("找到键 '" + key + "': " + config);
        } else {
            System.out.println("未找到键 '" + key + "'");
        }
    }
}
