package com.kering.cus.demo.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.kering.cus.lib.common.SortCriteria;
import com.kering.cus.lib.persistence.common.entity.PageResult;
import com.kering.cus.lib.persistence.mybatis.implementation.MyBatisBaseDAO;
import com.kering.cus.demo.entity.DemoEntity;
import com.kering.cus.demo.mapper.DemoMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import static com.baomidou.mybatisplus.core.toolkit.Constants.DESC;

@Repository
public class DemoDAO extends MyBatisBaseDAO<DemoEntity, DemoMapper, String> {

    /**
     * find demo by user id
     * @param userId the id of user
     * @return the user info in db
     */
    public DemoEntity findDemoByUserId(String userId) {
        LambdaQueryWrapper<DemoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DemoEntity::getUserId, userId);
        return entityMapper.selectOne(wrapper);
    }

    /**
     * find demo list page by position and sort
     * @param position  the position of user
     * @param pageNo the page number
     * @param pageSize the page size
     * @param sort the sort criteria
     * @return the page result
     */
    public PageResult<DemoEntity> findDemoListPageByPositionAndSort(String position, Integer pageNo, Integer pageSize, SortCriteria sort) {
        LambdaQueryWrapper<DemoEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(position)) {
            wrapper.eq(DemoEntity::getPosition, position);
        }
        if (ObjectUtils.isEmpty(sort)) {
            sort = SortCriteria.builder().build();
            sort.getCriteria().put("created_date", DESC);
        }
        return findPage(pageNo, pageSize, sort, wrapper);
    }

    /**
     * delete demo by user id
     * @param userId the id of user
     * @return the number of deleted rows
     */
    public int deleteByUserId(String userId) {
        return entityMapper.deleteByUserId(userId);
    }
}
