CREATE TABLE public.demo (
    id varchar(100) NOT NULL, -- 主键id
    user_id varchar(50) NOT NULL, -- 用户id
    "name" varchar(50) NULL, -- 姓名
    department_ids text NULL, -- 部门id集合
    "position" varchar(50) NULL, -- 职位
    email varchar(50) NULL, -- 邮箱
    wk_id varchar(50) NULL, -- 工作id
    created_date timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    created_by varchar(100) NULL, -- 创建人
    modified_date timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    modified_by varchar(100) NULL, -- 更新人
    deleted bool NOT NULL DEFAULT false, -- 是否删除
    tenant_id varchar(100) NULL, -- 租户id
    "version" int4 NOT NULL DEFAULT 0, -- 版本
    CONSTRAINT demo_pk PRIMARY KEY (id)
);

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN public.demo.id IS '主键id';
COMMENT ON COLUMN public.demo.user_id IS '用户id';
COMMENT ON COLUMN public.demo."name" IS '姓名';
COMMENT ON COLUMN public.demo.department_ids IS '部门id集合';
COMMENT ON COLUMN public.demo."position" IS '职位';
COMMENT ON COLUMN public.demo.email IS '邮箱';
COMMENT ON COLUMN public.demo.wk_id IS '工作id';
COMMENT ON COLUMN public.demo.created_date IS '创建时间';
COMMENT ON COLUMN public.demo.created_by IS '创建人';
COMMENT ON COLUMN public.demo.modified_date IS '更新时间';
COMMENT ON COLUMN public.demo.modified_by IS '更新人';
COMMENT ON COLUMN public.demo.deleted IS '是否删除';
COMMENT ON COLUMN public.demo.tenant_id IS '租户id';
COMMENT ON COLUMN public.demo."version" IS '版本';